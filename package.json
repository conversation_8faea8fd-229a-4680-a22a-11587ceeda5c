{"name": "blogmgmt_frontend", "version": "0.1.0", "private": true, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/icons-material": "^6.3.1", "@mui/material": "^6.3.1", "@mui/x-date-pickers": "^7.23.6", "@testing-library/react": "^16.1.0", "axios": "^1.7.9", "cra-template": "1.2.0", "date-fns": "^2.30.0", "dotenv": "^16.4.7", "react": "^19.0.0", "react-dom": "^19.0.0", "react-router-dom": "^7.1.1", "react-scripts": "5.0.1", "react-toastify": "^11.0.2"}, "scripts": {"start": "PORT=3001 react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"tailwindcss": "^3.4.17"}}